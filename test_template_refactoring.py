#!/usr/bin/env python
"""
Test script to verify template refactoring works correctly.
This script tests that all refactored templates can be rendered without errors.
"""

import os
import sys

# Add the project directory to Python path
project_dir = '/home/<USER>/Desktop/COZYWISH-PROJECT/CozyWish'
sys.path.insert(0, project_dir)

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')

import django
django.setup()

from django.conf import settings
from django.template.loader import get_template
from django.template import Context, RequestContext
from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser
from django.contrib.messages.storage.fallback import FallbackStorage

def test_template_rendering():
    """Test that all refactored templates can be rendered without errors."""
    
    # Create a mock request
    factory = RequestFactory()
    request = factory.get('/')
    request.user = AnonymousUser()
    
    # Add messages framework
    setattr(request, 'session', {})
    messages = FallbackStorage(request)
    setattr(request, '_messages', messages)
    
    # Test templates to check
    templates_to_test = [
        'accounts/base_auth.html',
        'accounts/components/form_field.html',
        'accounts/components/social_login.html',
        'accounts/components/auth_header.html',
        'accounts/components/auth_footer.html',
        'accounts/components/messages.html',
        'accounts/components/loading.html',
        'accounts/components/form_errors.html',
    ]
    
    print("Testing template components...")
    
    for template_name in templates_to_test:
        try:
            template = get_template(template_name)
            print(f"✓ {template_name} - Template loaded successfully")
            
            # Test basic rendering for components
            if 'components/' in template_name:
                if 'form_field.html' in template_name:
                    # Skip form_field as it requires a form field object
                    print(f"  → Skipping render test (requires form field)")
                    continue
                elif 'form_errors.html' in template_name:
                    # Skip form_errors as it requires a form object
                    print(f"  → Skipping render test (requires form object)")
                    continue
                else:
                    # Try to render other components
                    context = {'request': request}
                    rendered = template.render(context)
                    print(f"  → Rendered successfully ({len(rendered)} chars)")
            
        except Exception as e:
            print(f"✗ {template_name} - Error: {e}")
            return False
    
    print("\nTesting main authentication templates...")
    
    # Test main templates that use the components
    main_templates = [
        'allauth/account/login.html',
        'allauth/account/signup.html', 
        'allauth/socialaccount/login.html',
    ]
    
    for template_name in main_templates:
        try:
            template = get_template(template_name)
            print(f"✓ {template_name} - Template loaded successfully")
            
            # Create a basic context for rendering
            context = {
                'request': request,
                'user': request.user,
                'messages': [],
            }
            
            # Add form context for templates that need it
            if 'login.html' in template_name and 'socialaccount' not in template_name:
                # Mock login form
                from django import forms
                class MockLoginForm(forms.Form):
                    login = forms.EmailField()
                    password = forms.CharField(widget=forms.PasswordInput)
                    remember = forms.BooleanField(required=False)
                
                context['form'] = MockLoginForm()
                
            elif 'signup.html' in template_name:
                # Mock signup form
                from django import forms
                class MockSignupForm(forms.Form):
                    email = forms.EmailField()
                    password1 = forms.CharField(widget=forms.PasswordInput)
                    password2 = forms.CharField(widget=forms.PasswordInput)
                
                context['form'] = MockSignupForm()
            
            # Try to render
            rendered = template.render(context)
            print(f"  → Rendered successfully ({len(rendered)} chars)")
            
        except Exception as e:
            print(f"✗ {template_name} - Error: {e}")
            return False
    
    print("\n✅ All template tests passed!")
    return True

def test_component_features():
    """Test specific component features."""
    print("\nTesting component features...")
    
    # Test that components exist and have expected content
    component_tests = {
        'accounts/components/auth_header.html': ['auth-header', 'auth-icon', 'auth-title'],
        'accounts/components/social_login.html': ['social-login', 'social-btn'],
        'accounts/components/messages.html': ['auth-messages', 'auth-alert'],
        'accounts/components/loading.html': ['auth-loading', 'auth-spinner'],
    }
    
    for template_name, expected_classes in component_tests.items():
        try:
            template = get_template(template_name)
            template_source = template.template.source
            
            for css_class in expected_classes:
                if css_class in template_source:
                    print(f"✓ {template_name} contains '{css_class}'")
                else:
                    print(f"✗ {template_name} missing '{css_class}'")
                    
        except Exception as e:
            print(f"✗ Error testing {template_name}: {e}")
    
    print("✅ Component feature tests completed!")

if __name__ == '__main__':
    print("🧪 Testing Template Refactoring")
    print("=" * 50)
    
    success = test_template_rendering()
    test_component_features()
    
    if success:
        print("\n🎉 All tests passed! Template refactoring is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        sys.exit(1)
