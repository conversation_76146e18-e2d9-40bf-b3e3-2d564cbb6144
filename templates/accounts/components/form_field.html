{% comment %}
Reusable form field component for CozyWish authentication forms

Usage:
{% include 'accounts/components/form_field.html' with field=form.email field_type='email' placeholder='Enter your email address' autocomplete='email' %}

Parameters:
- field: The form field object (required)
- field_type: Type of field ('text', 'email', 'password', 'tel', etc.) - defaults to 'text'
- placeholder: Placeholder text for the field
- autocomplete: Autocomplete attribute value
- help_text: Additional help text to display
- required: Whether the field is required (defaults to field.field.required)
- floating_label: Whether to use floating label (defaults to true)
- show_errors: Whether to show validation errors (defaults to true)
- extra_classes: Additional CSS classes for the field
- wrapper_classes: Additional CSS classes for the wrapper div
{% endcomment %}

{% load widget_tweaks %}

{% with field_id=field.id_for_label field_name=field.name field_label=field.label %}
<div class="{% if floating_label|default:True %}form-floating{% endif %} mb-3 {{ wrapper_classes|default:'' }}">
    {% if field.field.widget.input_type == 'checkbox' or field.field.widget.input_type == 'radio' %}
        <!-- Checkbox/Radio field -->
        <div class="form-check">
            {{ field|add_class:"form-check-input"|add_class:extra_classes|default:"" }}
            <label class="form-check-label" for="{{ field_id }}">
                {{ field_label }}
                {% if required|default:field.field.required %}
                    <span class="text-danger" aria-label="required">*</span>
                {% endif %}
            </label>
            {% if help_text %}
                <div class="form-text">{{ help_text }}</div>
            {% endif %}
        </div>
    {% elif field.field.widget.input_type == 'select' %}
        <!-- Select field -->
        {{ field|add_class:"form-select"|add_class:extra_classes|default:"" }}
        {% if floating_label|default:True %}
            <label for="{{ field_id }}">
                {{ field_label }}
                {% if required|default:field.field.required %}
                    <span class="text-danger" aria-label="required">*</span>
                {% endif %}
            </label>
        {% endif %}
        {% if help_text %}
            <div class="form-text">{{ help_text }}</div>
        {% endif %}
    {% elif field.field.widget.input_type == 'textarea' %}
        <!-- Textarea field -->
        {{ field|add_class:"form-control"|add_class:extra_classes|default:""|attr:"rows:4" }}
        {% if floating_label|default:True %}
            <label for="{{ field_id }}">
                {{ field_label }}
                {% if required|default:field.field.required %}
                    <span class="text-danger" aria-label="required">*</span>
                {% endif %}
            </label>
        {% endif %}
        {% if help_text %}
            <div class="form-text">{{ help_text }}</div>
        {% endif %}
    {% else %}
        <!-- Regular input field -->
        {% with input_type=field_type|default:'text' %}
            {{ field|add_class:"form-control"|add_class:extra_classes|default:""|attr:"type:"|add:input_type }}
            {% if placeholder %}
                {% with current_attrs=field.field.widget.attrs %}
                    {{ field|attr:"placeholder:"|add:placeholder }}
                {% endwith %}
            {% endif %}
            {% if autocomplete %}
                {{ field|attr:"autocomplete:"|add:autocomplete }}
            {% endif %}
        {% endwith %}
        
        {% if floating_label|default:True %}
            <label for="{{ field_id }}">
                {{ field_label }}
                {% if required|default:field.field.required %}
                    <span class="text-danger" aria-label="required">*</span>
                {% endif %}
            </label>
        {% endif %}
        
        {% if help_text %}
            <div class="form-text">{{ help_text }}</div>
        {% endif %}
    {% endif %}

    <!-- Field validation errors -->
    {% if show_errors|default:True and field.errors %}
        <div class="invalid-feedback d-block">
            <i class="fas fa-exclamation-circle me-1"></i>
            {% for error in field.errors %}
                {{ error }}{% if not forloop.last %}<br>{% endif %}
            {% endfor %}
        </div>
    {% endif %}
</div>
{% endwith %}

{% comment %}
Additional CSS for enhanced styling (add to parent template if needed):

<style>
/* Enhanced form field styling */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--cw-brand-primary);
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.form-floating > .form-control:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
}

.form-check-input:focus {
    border-color: var(--cw-brand-primary);
    box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
}

.form-check-input:checked {
    background-color: var(--cw-brand-primary);
    border-color: var(--cw-brand-primary);
}

.invalid-feedback {
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.form-text {
    font-size: 0.9rem;
    color: var(--cw-neutral-600);
    margin-top: 0.5rem;
}

/* Required field indicator */
.text-danger {
    color: #dc3545 !important;
}

/* Accessibility improvements */
.form-control:focus,
.form-select:focus,
.form-check-input:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-control,
    .form-select,
    .form-check-input {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .form-control,
    .form-select,
    .form-check-input {
        transition: none;
    }
}
</style>
{% endcomment %}
